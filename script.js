"use strict";
// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==
// ===== CONFIGURAÇÕES =====
const CONFIG = {
    SCRIPT_NAME: "teste",
    VERSION: "1.0.30",
    STORAGE_KEYS: {
        SETTINGS: "settings",
    },
};
// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
    static get(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        }
        catch (_a) {
            return null;
        }
    }
    static setJSON(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        }
        catch (_a) {
            return false;
        }
    }
}
class DOMUtilities {
    static createElement(tag, options = {}) {
        const element = document.createElement(tag);
        if (options.className)
            element.className = options.className;
        if (options.textContent)
            element.textContent = options.textContent;
        if (options.styles)
            Object.assign(element.style, options.styles);
        if (options.attributes)
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        if (options.eventListeners)
            Object.entries(options.eventListeners).forEach(([event, listener]) => {
                element.addEventListener(event, listener);
            });
        return element;
    }
    static removeElement(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
            return true;
        }
        return false;
    }
}
class SettingsStore {
    constructor() {
        this.settings = {};
        this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
        this.loadSettings();
    }
    static getInstance() {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }
    loadSettings() {
        const savedSettings = StorageService.get(this.storageKey);
        if (savedSettings && typeof savedSettings === "object") {
            this.settings = savedSettings;
        }
    }
    saveSettings() {
        return StorageService.setJSON(this.storageKey, this.settings);
    }
    getAllSettings() {
        return Object.assign({}, this.settings);
    }
    getSetting(key) {
        return this.settings[key] || null;
    }
    setSetting(key, value) {
        this.settings[key] = value;
        return this.saveSettings();
    }
}
class KeyBindManager {
    constructor() {
        this.keyBindings = new Map();
        this.setupGlobalListener();
    }
    static getInstance() {
        if (!KeyBindManager.instance) {
            KeyBindManager.instance = new KeyBindManager();
        }
        return KeyBindManager.instance;
    }
    setupGlobalListener() {
        document.addEventListener("keydown", event => {
            const key = event.key.toLowerCase();
            const binding = this.keyBindings.get(key);
            if (binding) {
                const result = binding.handler(event);
                if (result !== false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        });
    }
    register(binding) {
        const key = binding.key.toLowerCase();
        this.keyBindings.set(key, binding);
        return true;
    }
    listBindings() {
        return Array.from(this.keyBindings.values());
    }
}
class UIManager {
    constructor() {
        this.isInitialized = false;
        this.keyBindManager = KeyBindManager.getInstance();
    }
    static getInstance() {
        if (!UIManager.instance)
            UIManager.instance = new UIManager();
        return UIManager.instance;
    }
    initialize() {
        if (this.isInitialized)
            return true;
        this.keyBindManager.register({
            key: "F1",
            handler: () => alert("Ajuda"),
            description: "Mostra a ajuda",
        });
        this.isInitialized = true;
        return true;
    }
    destroy() {
        this.isInitialized = false;
    }
}
class GameWorld {
    constructor() {
        this.cells = new Map();
        this.pellets = new Map();
        this.connections = new Map();
        this.views = new Map();
        this.activeConnection = "primary";
        this.synchronized = false;
        this.interceptedSockets = [];
        this.originalWebSocket = window.WebSocket;
        this.setupWebSocketInterception();
    }
    static getInstance() {
        if (!GameWorld.instance) {
            GameWorld.instance = new GameWorld();
        }
        return GameWorld.instance;
    }
    setupWebSocketInterception() {
        const self = this;
        // Interceptar WebSocket constructor
        window.WebSocket = class extends WebSocket {
            constructor(url, protocols) {
                super(url, protocols);
                // Verificar se é uma conexão do agario
                const urlString = url.toString();
                if (urlString.includes("agariobr.com.br") || urlString.includes("ws://") || urlString.includes("wss://")) {
                    console.log("WebSocket interceptado:", urlString);
                    self.interceptedSockets.push(this);
                    self.handleInterceptedSocket(this, urlString);
                }
            }
        };
    }
    handleInterceptedSocket(ws, url) {
        const connectionId = this.connections.size === 0 ? "primary" : `secondary_${this.connections.size}`;
        const connection = {
            id: connectionId,
            ws: ws,
            handshake: false,
            latency: 0,
            lastPing: Date.now(),
        };
        const view = {
            id: connectionId,
            camera: { x: 0, y: 0, scale: 1 },
            ownedCells: new Set(),
            visibleCells: new Set(),
            leaderboard: [],
            spawned: Date.now(),
            lastUpdate: Date.now(),
        };
        this.connections.set(connectionId, connection);
        this.views.set(connectionId, view);
        // Interceptar mensagens
        const originalOnMessage = ws.onmessage;
        ws.onmessage = event => {
            this.handleMessage(connectionId, event.data);
            if (originalOnMessage) {
                originalOnMessage.call(ws, event);
            }
        };
        // Interceptar conexão estabelecida
        const originalOnOpen = ws.onopen;
        ws.onopen = event => {
            connection.handshake = true;
            console.log(`Conexão ${connectionId} estabelecida`);
            if (originalOnOpen) {
                originalOnOpen.call(ws, event);
            }
        };
        // Interceptar desconexão
        const originalOnClose = ws.onclose;
        ws.onclose = event => {
            connection.handshake = false;
            console.log(`Conexão ${connectionId} fechada`);
            if (originalOnClose) {
                originalOnClose.call(ws, event);
            }
        };
    }
    handleMessage(connectionId, data) {
        try {
            const view = this.views.get(connectionId);
            if (!view)
                return;
            // Atualizar timestamp da view
            view.lastUpdate = Date.now();
            // Verificar se é ArrayBuffer (dados binários)
            if (data instanceof ArrayBuffer) {
                this.parseAgarioBRMessage(connectionId, new DataView(data));
            }
            else if (typeof data === "string") {
                // Mensagens de texto (possivelmente JSON)
                try {
                    const jsonData = JSON.parse(data);
                    this.handleJSONMessage(connectionId, jsonData);
                }
                catch (_a) {
                    console.log(`Mensagem de texto de ${connectionId}:`, data);
                }
            }
        }
        catch (error) {
            console.error(`Erro ao processar mensagem de ${connectionId}:`, error);
        }
    }
    parseAgarioBRMessage(connectionId, dataView) {
        if (dataView.byteLength === 0)
            return;
        const opcode = dataView.getUint8(0);
        const view = this.views.get(connectionId);
        if (!view)
            return;
        switch (opcode) {
            case 0x10: // Update cells (comum em jogos agar.io)
                this.handleCellUpdate(connectionId, dataView);
                break;
            case 0x11: // Update position
                this.handlePositionUpdate(connectionId, dataView);
                break;
            case 0x12: // Update leaderboard
                this.handleLeaderboardUpdate(connectionId, dataView);
                break;
            case 0x14: // Clear cells
                this.handleClearCells(connectionId);
                break;
            case 0x20: // Remove cells
                this.handleRemoveCells(connectionId, dataView);
                break;
            case 0x30: // Set border
                this.handleSetBorder(connectionId, dataView);
                break;
            case 0x40: // Set own cells
                this.handleSetOwnCells(connectionId, dataView);
                break;
            default:
                console.log(`Opcode desconhecido ${opcode.toString(16)} de ${connectionId}`);
        }
    }
    handleCellUpdate(connectionId, dataView) {
        let offset = 1;
        const view = this.views.get(connectionId);
        if (!view)
            return;
        while (offset < dataView.byteLength) {
            try {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                if (cellId === 0)
                    break; // End marker
                const x = dataView.getFloat32(offset, true);
                offset += 4;
                const y = dataView.getFloat32(offset, true);
                offset += 4;
                const size = dataView.getFloat32(offset, true);
                offset += 4;
                // Ler cor (3 bytes RGB)
                const r = dataView.getUint8(offset++);
                const g = dataView.getUint8(offset++);
                const b = dataView.getUint8(offset++);
                const color = `rgb(${r},${g},${b})`;
                // Ler flags
                const flags = dataView.getUint8(offset++);
                const isVirus = (flags & 0x01) !== 0;
                const isEjected = (flags & 0x02) !== 0;
                // Ler nome (se presente)
                let name = "";
                if (!isVirus && !isEjected) {
                    const nameLength = dataView.getUint16(offset, true);
                    offset += 2;
                    if (nameLength > 0) {
                        const nameBytes = new Uint8Array(dataView.buffer, offset, nameLength * 2);
                        name = new TextDecoder("utf-16le").decode(nameBytes);
                        offset += nameLength * 2;
                    }
                }
                // Atualizar ou criar célula
                if (!this.cells.has(cellId)) {
                    this.cells.set(cellId, {
                        id: cellId,
                        views: new Map(),
                    });
                }
                const cell = this.cells.get(cellId);
                cell.views.set(connectionId, {
                    x,
                    y,
                    size,
                    color,
                    name,
                    timestamp: Date.now(),
                });
                view.visibleCells.add(cellId);
            }
            catch (error) {
                console.error("Erro ao parsear célula:", error);
                break;
            }
        }
    }
    handlePositionUpdate(connectionId, dataView) {
        if (dataView.byteLength >= 17) {
            const view = this.views.get(connectionId);
            if (view) {
                view.camera.x = dataView.getFloat32(1, true);
                view.camera.y = dataView.getFloat32(5, true);
                view.camera.scale = dataView.getFloat32(9, true);
            }
        }
    }
    handleLeaderboardUpdate(connectionId, dataView) {
        const view = this.views.get(connectionId);
        if (!view)
            return;
        view.leaderboard = [];
        let offset = 1;
        try {
            const count = dataView.getUint32(offset, true);
            offset += 4;
            for (let i = 0; i < count && offset < dataView.byteLength; i++) {
                const isMe = dataView.getUint32(offset, true) === 1;
                offset += 4;
                const nameLength = dataView.getUint16(offset, true);
                offset += 2;
                let name = "";
                if (nameLength > 0) {
                    const nameBytes = new Uint8Array(dataView.buffer, offset, nameLength * 2);
                    name = new TextDecoder("utf-16le").decode(nameBytes);
                    offset += nameLength * 2;
                }
                view.leaderboard.push({ name, isMe });
            }
        }
        catch (error) {
            console.error("Erro ao parsear leaderboard:", error);
        }
    }
    handleClearCells(connectionId) {
        const view = this.views.get(connectionId);
        if (view) {
            view.visibleCells.clear();
            view.ownedCells.clear();
        }
        // Remover células desta view
        for (const [cellId, cell] of this.cells) {
            cell.views.delete(connectionId);
            if (cell.views.size === 0) {
                this.cells.delete(cellId);
            }
        }
    }
    handleRemoveCells(connectionId, dataView) {
        let offset = 1;
        const view = this.views.get(connectionId);
        while (offset < dataView.byteLength) {
            const cellId = dataView.getUint32(offset, true);
            offset += 4;
            if (cellId === 0)
                break;
            // Remover da view
            if (view) {
                view.visibleCells.delete(cellId);
                view.ownedCells.delete(cellId);
            }
            // Remover da célula global
            const cell = this.cells.get(cellId);
            if (cell) {
                cell.views.delete(connectionId);
                if (cell.views.size === 0) {
                    this.cells.delete(cellId);
                }
            }
        }
    }
    handleSetBorder(connectionId, dataView) {
        if (dataView.byteLength >= 33) {
            const view = this.views.get(connectionId);
            if (view) {
                // Bordas do mapa (left, top, right, bottom)
                console.log(`Bordas do mapa para ${connectionId}:`, {
                    left: dataView.getFloat64(1, true),
                    top: dataView.getFloat64(9, true),
                    right: dataView.getFloat64(17, true),
                    bottom: dataView.getFloat64(25, true),
                });
            }
        }
    }
    handleSetOwnCells(connectionId, dataView) {
        const view = this.views.get(connectionId);
        if (!view)
            return;
        view.ownedCells.clear();
        let offset = 1;
        try {
            const count = dataView.getUint16(offset, true);
            offset += 2;
            for (let i = 0; i < count && offset < dataView.byteLength; i++) {
                const cellId = dataView.getUint32(offset, true);
                offset += 4;
                view.ownedCells.add(cellId);
            }
            console.log(`${connectionId} possui ${view.ownedCells.size} células`);
        }
        catch (error) {
            console.error("Erro ao parsear células próprias:", error);
        }
    }
    handleJSONMessage(connectionId, data) {
        console.log(`Mensagem JSON de ${connectionId}:`, data);
        // Implementar parsing de mensagens JSON específicas do agariobr.com.br
    }
    createSecondaryConnection() {
        try {
            // Verificar se já existe uma conexão secundária
            if (this.connections.has("secondary_1")) {
                console.log("Conexão secundária já existe");
                return true;
            }
            // Tentar criar uma segunda conexão usando a mesma URL da primeira
            const primaryConnection = this.connections.get("primary");
            if (!(primaryConnection === null || primaryConnection === void 0 ? void 0 : primaryConnection.ws)) {
                console.error("Conexão primária não encontrada");
                return false;
            }
            // Extrair URL da conexão primária
            const primaryUrl = primaryConnection.ws.url;
            console.log("Criando conexão secundária para:", primaryUrl);
            // Criar nova conexão (será interceptada automaticamente)
            new this.originalWebSocket(primaryUrl);
            return true;
        }
        catch (error) {
            console.error("Erro ao criar conexão secundária:", error);
            return false;
        }
    }
    synchronizeCells() {
        if (!this.synchronized)
            return;
        // Sincronizar células entre todas as views
        for (const [cellId, cell] of this.cells) {
            const viewData = Array.from(cell.views.values());
            if (viewData.length > 1) {
                // Calcular posição média ponderada por timestamp
                let totalWeight = 0;
                let avgX = 0, avgY = 0, avgSize = 0;
                let latestColor = "";
                let latestName = "";
                let latestTimestamp = 0;
                for (const data of viewData) {
                    const age = Date.now() - data.timestamp;
                    const weight = Math.max(0.1, 1 / (age + 100)); // Peso baseado na idade
                    totalWeight += weight;
                    avgX += data.x * weight;
                    avgY += data.y * weight;
                    avgSize += data.size * weight;
                    // Usar dados mais recentes para cor e nome
                    if (data.timestamp > latestTimestamp) {
                        latestTimestamp = data.timestamp;
                        latestColor = data.color;
                        latestName = data.name;
                    }
                }
                cell.merged = {
                    x: avgX / totalWeight,
                    y: avgY / totalWeight,
                    size: avgSize / totalWeight,
                    color: latestColor,
                    name: latestName,
                };
            }
            else if (viewData.length === 1) {
                // Se só há uma view, usar seus dados diretamente
                const data = viewData[0];
                cell.merged = {
                    x: data.x,
                    y: data.y,
                    size: data.size,
                    color: data.color,
                    name: data.name,
                };
            }
        }
    }
    switchConnection(connectionId) {
        if (!this.connections.has(connectionId)) {
            console.error(`Conexão ${connectionId} não encontrada`);
            return false;
        }
        if (this.activeConnection === connectionId) {
            return true;
        }
        console.log(`Alternando de ${this.activeConnection} para ${connectionId}`);
        this.activeConnection = connectionId;
        // Aqui você pode adicionar lógica para atualizar a UI
        this.updateUI();
        return true;
    }
    updateUI() {
        // Implementar atualização da interface baseada na conexão ativa
        const activeView = this.views.get(this.activeConnection);
        if (activeView) {
            console.log(`UI atualizada para conexão ${this.activeConnection}`);
            this.updateConnectionStatus();
        }
    }
    updateConnectionStatus() {
        // Criar ou atualizar indicador visual das conexões
        let statusDiv = document.getElementById("multibox-status");
        if (!statusDiv) {
            statusDiv = document.createElement("div");
            statusDiv.id = "multibox-status";
            statusDiv.style.cssText = `
				position: fixed;
				top: 10px;
				right: 10px;
				background: rgba(0, 0, 0, 0.8);
				color: white;
				padding: 10px;
				border-radius: 5px;
				font-family: monospace;
				font-size: 12px;
				z-index: 10000;
				min-width: 200px;
			`;
            document.body.appendChild(statusDiv);
        }
        let html = "<div><strong>🎮 Multibox Status</strong></div>";
        html += `<div>Sincronização: ${this.synchronized ? "✅ ON" : "❌ OFF"}</div>`;
        html += "<div>─────────────────</div>";
        for (const [id, connection] of this.connections) {
            const view = this.views.get(id);
            const isActive = id === this.activeConnection;
            const status = connection.handshake ? "🟢" : "🔴";
            const activeIndicator = isActive ? "👉 " : "   ";
            html += `<div>${activeIndicator}${status} ${id}</div>`;
            if (view) {
                html += `<div>   Células: ${view.ownedCells.size} próprias, ${view.visibleCells.size} visíveis</div>`;
                if (connection.latency > 0) {
                    html += `<div>   Ping: ${connection.latency}ms</div>`;
                }
            }
        }
        html += "<div>─────────────────</div>";
        html += "<div><small>Ctrl+Tab: Alternar</small></div>";
        html += "<div><small>F2: Nova conexão</small></div>";
        html += "<div><small>F3: Sync on/off</small></div>";
        statusDiv.innerHTML = html;
    }
    startSynchronizationLoop() {
        // Loop de sincronização a cada 50ms
        setInterval(() => {
            this.synchronizeCells();
            this.updateConnectionStatus();
        }, 50);
    }
    getActiveConnection() {
        return this.connections.get(this.activeConnection);
    }
    getActiveView() {
        return this.views.get(this.activeConnection);
    }
    getAllConnections() {
        return Array.from(this.connections.values());
    }
    // Métodos para enviar comandos para as conexões
    sendToConnection(connectionId, data) {
        const connection = this.connections.get(connectionId);
        if (!(connection === null || connection === void 0 ? void 0 : connection.ws) || connection.ws.readyState !== WebSocket.OPEN) {
            return false;
        }
        try {
            connection.ws.send(data);
            return true;
        }
        catch (error) {
            console.error(`Erro ao enviar dados para ${connectionId}:`, error);
            return false;
        }
    }
    sendToActiveConnection(data) {
        return this.sendToConnection(this.activeConnection, data);
    }
    sendToAllConnections(data) {
        let sent = 0;
        for (const [connectionId] of this.connections) {
            if (this.sendToConnection(connectionId, data)) {
                sent++;
            }
        }
        return sent;
    }
    // Comandos específicos do agar.io
    sendMovement(connectionId, x, y) {
        const buffer = new ArrayBuffer(13);
        const view = new DataView(buffer);
        view.setUint8(0, 0x10); // Opcode para movimento
        view.setFloat32(1, x, true);
        view.setFloat32(5, y, true);
        view.setUint32(9, Date.now() & 0xffffffff, true); // Timestamp
        return this.sendToConnection(connectionId, buffer);
    }
    sendSplit(connectionId) {
        const buffer = new ArrayBuffer(1);
        const view = new DataView(buffer);
        view.setUint8(0, 0x11); // Opcode para split
        return this.sendToConnection(connectionId, buffer);
    }
    sendEject(connectionId) {
        const buffer = new ArrayBuffer(1);
        const view = new DataView(buffer);
        view.setUint8(0, 0x12); // Opcode para ejetar massa
        return this.sendToConnection(connectionId, buffer);
    }
    sendSpawn(connectionId, name) {
        const nameBytes = new TextEncoder().encode(name);
        const buffer = new ArrayBuffer(1 + nameBytes.length);
        const view = new DataView(buffer);
        view.setUint8(0, 0x00); // Opcode para spawn
        // Copiar nome
        const uint8View = new Uint8Array(buffer);
        uint8View.set(nameBytes, 1);
        return this.sendToConnection(connectionId, buffer);
    }
}
// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
    constructor() {
        this.isInitialized = false;
        this.settingsStore = SettingsStore.getInstance();
        this.uiManager = UIManager.getInstance();
        this.gameWorld = GameWorld.getInstance();
    }
    static getInstance() {
        if (!ScriptApplication.instance)
            ScriptApplication.instance = new ScriptApplication();
        return ScriptApplication.instance;
    }
    async initialize() {
        if (this.isInitialized)
            return true;
        if (document.readyState === "loading")
            await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
        // Inicializar UI
        this.uiManager.initialize();
        // Configurar keybinds para multiboxing
        this.setupMultiboxKeybinds();
        // Configurar settings
        this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
        this.settingsStore.setSetting("multiboxEnabled", true);
        // Iniciar loop de sincronização
        this.gameWorld.startSynchronizationLoop();
        console.log("Script inicializado com sistema de multiboxing");
        this.isInitialized = true;
        return true;
    }
    setupMultiboxKeybinds() {
        const keyBindManager = KeyBindManager.getInstance();
        // Tab para alternar entre conexões
        keyBindManager.register({
            key: "tab",
            handler: event => {
                if (event.ctrlKey) {
                    const connections = this.gameWorld.getAllConnections();
                    if (connections.length > 1) {
                        const currentIndex = connections.findIndex(c => c.id === this.gameWorld.activeConnection);
                        const nextIndex = (currentIndex + 1) % connections.length;
                        this.gameWorld.switchConnection(connections[nextIndex].id);
                    }
                    return true; // Prevenir comportamento padrão
                }
                return false;
            },
            description: "Ctrl+Tab: Alternar entre conexões",
        });
        // F2 para criar conexão secundária
        keyBindManager.register({
            key: "f2",
            handler: () => {
                this.gameWorld.createSecondaryConnection();
                return true;
            },
            description: "F2: Criar conexão secundária",
        });
        // F3 para alternar sincronização
        keyBindManager.register({
            key: "f3",
            handler: () => {
                this.gameWorld.synchronized = !this.gameWorld.synchronized;
                console.log(`Sincronização ${this.gameWorld.synchronized ? "ativada" : "desativada"}`);
                return true;
            },
            description: "F3: Alternar sincronização de views",
        });
    }
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
